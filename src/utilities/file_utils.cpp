#include "utilities/file_utils.h"
#include "utilities/string_utils.h"
#include <fstream>
#include <iostream>

namespace pixiv_downloader {
namespace utilities {

bool FileUtils::FileExists(const std::string& file_path) {
    return std::filesystem::exists(file_path) && std::filesystem::is_regular_file(file_path);
}

bool FileUtils::DirectoryExists(const std::string& dir_path) {
    return std::filesystem::exists(dir_path) && std::filesystem::is_directory(dir_path);
}

bool FileUtils::CreateDirectory(const std::string& dir_path) {
    try {
        return std::filesystem::create_directories(dir_path);
    } catch (const std::filesystem::filesystem_error& e) {
        std::cerr << "创建目录失败: " << e.what() << std::endl;
        return false;
    }
}

bool FileUtils::DeleteFile(const std::string& file_path) {
    try {
        return std::filesystem::remove(file_path);
    } catch (const std::filesystem::filesystem_error& e) {
        std::cerr << "删除文件失败: " << e.what() << std::endl;
        return false;
    }
}

bool FileUtils::DeleteDirectory(const std::string& dir_path) {
    try {
        return std::filesystem::remove_all(dir_path) > 0;
    } catch (const std::filesystem::filesystem_error& e) {
        std::cerr << "删除目录失败: " << e.what() << std::endl;
        return false;
    }
}

size_t FileUtils::GetFileSize(const std::string& file_path) {
    try {
        if (FileExists(file_path)) {
            return std::filesystem::file_size(file_path);
        }
    } catch (const std::filesystem::filesystem_error& e) {
        std::cerr << "获取文件大小失败: " << e.what() << std::endl;
    }
    return 0;
}

std::string FileUtils::GetFileExtension(const std::string& file_path) {
    std::filesystem::path path(file_path);
    return path.extension().string();
}

std::string FileUtils::GetFileName(const std::string& file_path) {
    std::filesystem::path path(file_path);
    return path.filename().string();
}

std::string FileUtils::GetFileNameWithoutExtension(const std::string& file_path) {
    std::filesystem::path path(file_path);
    return path.stem().string();
}

std::string FileUtils::GetDirectoryPath(const std::string& file_path) {
    std::filesystem::path path(file_path);
    return path.parent_path().string();
}

std::string FileUtils::JoinPath(const std::vector<std::string>& paths) {
    if (paths.empty()) {
        return "";
    }

    std::filesystem::path result(paths[0]);
    for (size_t i = 1; i < paths.size(); ++i) {
        result /= paths[i];
    }
    return result.string();
}

std::string FileUtils::JoinPath(const std::string& path1, const std::string& path2) {
    std::filesystem::path result(path1);
    result /= path2;
    return result.string();
}

std::string FileUtils::NormalizePath(const std::string& path) {
    try {
        std::filesystem::path normalized = std::filesystem::path(path).lexically_normal();
        return normalized.string();
    } catch (const std::exception& e) {
        std::cerr << "路径规范化失败: " << e.what() << std::endl;
        return path;
    }
}

std::string FileUtils::GetAbsolutePath(const std::string& path) {
    try {
        return std::filesystem::absolute(path).string();
    } catch (const std::filesystem::filesystem_error& e) {
        std::cerr << "获取绝对路径失败: " << e.what() << std::endl;
        return path;
    }
}

std::string FileUtils::ReadFileToString(const std::string& file_path) {
    std::ifstream file(file_path, std::ios::binary);
    if (!file.is_open()) {
        return "";
    }

    file.seekg(0, std::ios::end);
    size_t size = file.tellg();
    file.seekg(0, std::ios::beg);

    std::string content(size, '\0');
    file.read(&content[0], size);
    return content;
}

bool FileUtils::WriteStringToFile(const std::string& file_path, const std::string& content, bool append) {
    std::ios::openmode mode = std::ios::binary;
    if (append) {
        mode |= std::ios::app;
    } else {
        mode |= std::ios::trunc;
    }

    std::ofstream file(file_path, mode);
    if (!file.is_open()) {
        return false;
    }

    file.write(content.c_str(), content.size());
    return file.good();
}

bool FileUtils::CopyFile(const std::string& source_path, const std::string& dest_path) {
    try {
        std::filesystem::copy_file(source_path, dest_path, 
                                  std::filesystem::copy_options::overwrite_existing);
        return true;
    } catch (const std::filesystem::filesystem_error& e) {
        std::cerr << "复制文件失败: " << e.what() << std::endl;
        return false;
    }
}

bool FileUtils::MoveFile(const std::string& source_path, const std::string& dest_path) {
    try {
        std::filesystem::rename(source_path, dest_path);
        return true;
    } catch (const std::filesystem::filesystem_error& e) {
        std::cerr << "移动文件失败: " << e.what() << std::endl;
        return false;
    }
}

std::string FileUtils::GenerateUniqueFileName(const std::string& base_path, const std::string& suffix) {
    if (!FileExists(base_path)) {
        return base_path;
    }

    std::string directory = GetDirectoryPath(base_path);
    std::string filename = GetFileNameWithoutExtension(base_path);
    std::string extension = GetFileExtension(base_path);

    int counter = 1;
    std::string new_path;
    do {
        std::string new_filename = filename + StringUtils::Format(suffix, counter) + extension;
        new_path = JoinPath(directory, new_filename);
        counter++;
    } while (FileExists(new_path));

    return new_path;
}

bool FileUtils::IsPathTooLong(const std::string& path) {
    return path.length() > MAX_PATH_LENGTH;
}

std::string FileUtils::GetCurrentWorkingDirectory() {
    try {
        return std::filesystem::current_path().string();
    } catch (const std::filesystem::filesystem_error& e) {
        std::cerr << "获取当前工作目录失败: " << e.what() << std::endl;
        return "";
    }
}

std::string FileUtils::GetTempDirectory() {
    try {
        return std::filesystem::temp_directory_path().string();
    } catch (const std::filesystem::filesystem_error& e) {
        std::cerr << "获取临时目录失败: " << e.what() << std::endl;
        return "";
    }
}

std::vector<std::string> FileUtils::ListFiles(const std::string& dir_path, bool recursive) {
    std::vector<std::string> files;
    try {
        if (recursive) {
            for (const auto& entry : std::filesystem::recursive_directory_iterator(dir_path)) {
                if (entry.is_regular_file()) {
                    files.push_back(entry.path().string());
                }
            }
        } else {
            for (const auto& entry : std::filesystem::directory_iterator(dir_path)) {
                if (entry.is_regular_file()) {
                    files.push_back(entry.path().string());
                }
            }
        }
    } catch (const std::filesystem::filesystem_error& e) {
        std::cerr << "列出文件失败: " << e.what() << std::endl;
    }
    return files;
}

std::vector<std::string> FileUtils::ListDirectories(const std::string& dir_path, bool recursive) {
    std::vector<std::string> directories;
    try {
        if (recursive) {
            for (const auto& entry : std::filesystem::recursive_directory_iterator(dir_path)) {
                if (entry.is_directory()) {
                    directories.push_back(entry.path().string());
                }
            }
        } else {
            for (const auto& entry : std::filesystem::directory_iterator(dir_path)) {
                if (entry.is_directory()) {
                    directories.push_back(entry.path().string());
                }
            }
        }
    } catch (const std::filesystem::filesystem_error& e) {
        std::cerr << "列出目录失败: " << e.what() << std::endl;
    }
    return directories;
}

} // namespace utilities
} // namespace pixiv_downloader
