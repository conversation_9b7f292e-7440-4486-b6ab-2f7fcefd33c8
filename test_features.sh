#!/bin/bash

# PixivTagDownloader 功能测试脚本

echo "=== PixivTagDownloader 功能测试 ==="

cd build

# 1. 测试版本信息
echo "1. 测试版本信息..."
./PixivTagDownloader --version
echo ""

# 2. 测试帮助信息
echo "2. 测试帮助信息..."
./PixivTagDownloader --help | head -10
echo ""

# 3. 测试配置文件加载
echo "3. 测试配置文件加载..."
timeout 5 ./PixivTagDownloader -u 28925283 --all-works 2>&1 | head -10
echo ""

# 4. 测试交互模式（模拟输入）
echo "4. 测试交互模式..."
echo -e "4\n28925283\n1\nn" | timeout 10 ./PixivTagDownloader -i 2>&1 | head -15
echo ""

# 5. 测试Aria2c检查
echo "5. 测试Aria2c可用性..."
if command -v aria2c &> /dev/null; then
    echo "✓ Aria2c 已安装: $(aria2c --version | head -1)"
else
    echo "✗ Aria2c 未安装"
fi
echo ""

# 6. 测试标签提取功能（模拟）
echo "6. 测试标签提取功能..."
echo -e "1\n28925283\n3\n5\ndone\nn" | timeout 15 ./PixivTagDownloader -i 2>&1 | tail -10
echo ""

echo "=== 功能测试完成 ==="

# 显示编译信息
echo ""
echo "=== 编译信息 ==="
echo "编译时间: $(./PixivTagDownloader --version | grep '构建时间')"
echo "可执行文件大小: $(ls -lh PixivTagDownloader | awk '{print $5}')"
echo "依赖库检查:"
ldd PixivTagDownloader | grep -E "(curl|yaml|nlohmann)" || echo "  基本依赖库已链接"
