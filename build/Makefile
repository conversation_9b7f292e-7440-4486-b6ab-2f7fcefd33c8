# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/wd500g/PixivTagDownloader-CPP

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/wd500g/PixivTagDownloader-CPP/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles /mnt/wd500g/PixivTagDownloader-CPP/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named PixivTagDownloader

# Build rule for target.
PixivTagDownloader: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 PixivTagDownloader
.PHONY : PixivTagDownloader

# fast build rule for target.
PixivTagDownloader/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/build
.PHONY : PixivTagDownloader/fast

src/api/pixiv_api_client.o: src/api/pixiv_api_client.cpp.o
.PHONY : src/api/pixiv_api_client.o

# target to build an object file
src/api/pixiv_api_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api_client.cpp.o
.PHONY : src/api/pixiv_api_client.cpp.o

src/api/pixiv_api_client.i: src/api/pixiv_api_client.cpp.i
.PHONY : src/api/pixiv_api_client.i

# target to preprocess a source file
src/api/pixiv_api_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api_client.cpp.i
.PHONY : src/api/pixiv_api_client.cpp.i

src/api/pixiv_api_client.s: src/api/pixiv_api_client.cpp.s
.PHONY : src/api/pixiv_api_client.s

# target to generate assembly for a file
src/api/pixiv_api_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api_client.cpp.s
.PHONY : src/api/pixiv_api_client.cpp.s

src/auth/pixiv_auth.o: src/auth/pixiv_auth.cpp.o
.PHONY : src/auth/pixiv_auth.o

# target to build an object file
src/auth/pixiv_auth.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/auth/pixiv_auth.cpp.o
.PHONY : src/auth/pixiv_auth.cpp.o

src/auth/pixiv_auth.i: src/auth/pixiv_auth.cpp.i
.PHONY : src/auth/pixiv_auth.i

# target to preprocess a source file
src/auth/pixiv_auth.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/auth/pixiv_auth.cpp.i
.PHONY : src/auth/pixiv_auth.cpp.i

src/auth/pixiv_auth.s: src/auth/pixiv_auth.cpp.s
.PHONY : src/auth/pixiv_auth.s

# target to generate assembly for a file
src/auth/pixiv_auth.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/auth/pixiv_auth.cpp.s
.PHONY : src/auth/pixiv_auth.cpp.s

src/cli/cli_handler.o: src/cli/cli_handler.cpp.o
.PHONY : src/cli/cli_handler.o

# target to build an object file
src/cli/cli_handler.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/cli/cli_handler.cpp.o
.PHONY : src/cli/cli_handler.cpp.o

src/cli/cli_handler.i: src/cli/cli_handler.cpp.i
.PHONY : src/cli/cli_handler.i

# target to preprocess a source file
src/cli/cli_handler.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/cli/cli_handler.cpp.i
.PHONY : src/cli/cli_handler.cpp.i

src/cli/cli_handler.s: src/cli/cli_handler.cpp.s
.PHONY : src/cli/cli_handler.s

# target to generate assembly for a file
src/cli/cli_handler.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/cli/cli_handler.cpp.s
.PHONY : src/cli/cli_handler.cpp.s

src/config/config_manager.o: src/config/config_manager.cpp.o
.PHONY : src/config/config_manager.o

# target to build an object file
src/config/config_manager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.o
.PHONY : src/config/config_manager.cpp.o

src/config/config_manager.i: src/config/config_manager.cpp.i
.PHONY : src/config/config_manager.i

# target to preprocess a source file
src/config/config_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.i
.PHONY : src/config/config_manager.cpp.i

src/config/config_manager.s: src/config/config_manager.cpp.s
.PHONY : src/config/config_manager.s

# target to generate assembly for a file
src/config/config_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.s
.PHONY : src/config/config_manager.cpp.s

src/core/main_controller.o: src/core/main_controller.cpp.o
.PHONY : src/core/main_controller.o

# target to build an object file
src/core/main_controller.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/core/main_controller.cpp.o
.PHONY : src/core/main_controller.cpp.o

src/core/main_controller.i: src/core/main_controller.cpp.i
.PHONY : src/core/main_controller.i

# target to preprocess a source file
src/core/main_controller.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/core/main_controller.cpp.i
.PHONY : src/core/main_controller.cpp.i

src/core/main_controller.s: src/core/main_controller.cpp.s
.PHONY : src/core/main_controller.s

# target to generate assembly for a file
src/core/main_controller.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/core/main_controller.cpp.s
.PHONY : src/core/main_controller.cpp.s

src/download/downloader_core.o: src/download/downloader_core.cpp.o
.PHONY : src/download/downloader_core.o

# target to build an object file
src/download/downloader_core.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/download/downloader_core.cpp.o
.PHONY : src/download/downloader_core.cpp.o

src/download/downloader_core.i: src/download/downloader_core.cpp.i
.PHONY : src/download/downloader_core.i

# target to preprocess a source file
src/download/downloader_core.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/download/downloader_core.cpp.i
.PHONY : src/download/downloader_core.cpp.i

src/download/downloader_core.s: src/download/downloader_core.cpp.s
.PHONY : src/download/downloader_core.s

# target to generate assembly for a file
src/download/downloader_core.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/download/downloader_core.cpp.s
.PHONY : src/download/downloader_core.cpp.s

src/main.o: src/main.cpp.o
.PHONY : src/main.o

# target to build an object file
src/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o
.PHONY : src/main.cpp.o

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

src/storage/storage_manager.o: src/storage/storage_manager.cpp.o
.PHONY : src/storage/storage_manager.o

# target to build an object file
src/storage/storage_manager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o
.PHONY : src/storage/storage_manager.cpp.o

src/storage/storage_manager.i: src/storage/storage_manager.cpp.i
.PHONY : src/storage/storage_manager.i

# target to preprocess a source file
src/storage/storage_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.i
.PHONY : src/storage/storage_manager.cpp.i

src/storage/storage_manager.s: src/storage/storage_manager.cpp.s
.PHONY : src/storage/storage_manager.s

# target to generate assembly for a file
src/storage/storage_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.s
.PHONY : src/storage/storage_manager.cpp.s

src/utilities/file_utils.o: src/utilities/file_utils.cpp.o
.PHONY : src/utilities/file_utils.o

# target to build an object file
src/utilities/file_utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utilities/file_utils.cpp.o
.PHONY : src/utilities/file_utils.cpp.o

src/utilities/file_utils.i: src/utilities/file_utils.cpp.i
.PHONY : src/utilities/file_utils.i

# target to preprocess a source file
src/utilities/file_utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utilities/file_utils.cpp.i
.PHONY : src/utilities/file_utils.cpp.i

src/utilities/file_utils.s: src/utilities/file_utils.cpp.s
.PHONY : src/utilities/file_utils.s

# target to generate assembly for a file
src/utilities/file_utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utilities/file_utils.cpp.s
.PHONY : src/utilities/file_utils.cpp.s

src/utilities/logger.o: src/utilities/logger.cpp.o
.PHONY : src/utilities/logger.o

# target to build an object file
src/utilities/logger.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utilities/logger.cpp.o
.PHONY : src/utilities/logger.cpp.o

src/utilities/logger.i: src/utilities/logger.cpp.i
.PHONY : src/utilities/logger.i

# target to preprocess a source file
src/utilities/logger.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utilities/logger.cpp.i
.PHONY : src/utilities/logger.cpp.i

src/utilities/logger.s: src/utilities/logger.cpp.s
.PHONY : src/utilities/logger.s

# target to generate assembly for a file
src/utilities/logger.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utilities/logger.cpp.s
.PHONY : src/utilities/logger.cpp.s

src/utilities/string_utils.o: src/utilities/string_utils.cpp.o
.PHONY : src/utilities/string_utils.o

# target to build an object file
src/utilities/string_utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utilities/string_utils.cpp.o
.PHONY : src/utilities/string_utils.cpp.o

src/utilities/string_utils.i: src/utilities/string_utils.cpp.i
.PHONY : src/utilities/string_utils.i

# target to preprocess a source file
src/utilities/string_utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utilities/string_utils.cpp.i
.PHONY : src/utilities/string_utils.cpp.i

src/utilities/string_utils.s: src/utilities/string_utils.cpp.s
.PHONY : src/utilities/string_utils.s

# target to generate assembly for a file
src/utilities/string_utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utilities/string_utils.cpp.s
.PHONY : src/utilities/string_utils.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... PixivTagDownloader"
	@echo "... src/api/pixiv_api_client.o"
	@echo "... src/api/pixiv_api_client.i"
	@echo "... src/api/pixiv_api_client.s"
	@echo "... src/auth/pixiv_auth.o"
	@echo "... src/auth/pixiv_auth.i"
	@echo "... src/auth/pixiv_auth.s"
	@echo "... src/cli/cli_handler.o"
	@echo "... src/cli/cli_handler.i"
	@echo "... src/cli/cli_handler.s"
	@echo "... src/config/config_manager.o"
	@echo "... src/config/config_manager.i"
	@echo "... src/config/config_manager.s"
	@echo "... src/core/main_controller.o"
	@echo "... src/core/main_controller.i"
	@echo "... src/core/main_controller.s"
	@echo "... src/download/downloader_core.o"
	@echo "... src/download/downloader_core.i"
	@echo "... src/download/downloader_core.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
	@echo "... src/storage/storage_manager.o"
	@echo "... src/storage/storage_manager.i"
	@echo "... src/storage/storage_manager.s"
	@echo "... src/utilities/file_utils.o"
	@echo "... src/utilities/file_utils.i"
	@echo "... src/utilities/file_utils.s"
	@echo "... src/utilities/logger.o"
	@echo "... src/utilities/logger.i"
	@echo "... src/utilities/logger.s"
	@echo "... src/utilities/string_utils.o"
	@echo "... src/utilities/string_utils.i"
	@echo "... src/utilities/string_utils.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

