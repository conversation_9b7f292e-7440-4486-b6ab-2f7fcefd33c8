file(REMOVE_RECURSE
  "CMakeFiles/PixivTagDownloader.dir/link.d"
  "CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api_client.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api_client.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/auth/pixiv_auth.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/auth/pixiv_auth.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/cli/cli_handler.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/cli/cli_handler.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/core/main_controller.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/core/main_controller.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/download/downloader_core.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/download/downloader_core.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/utilities/file_utils.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/utilities/file_utils.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/utilities/logger.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/utilities/logger.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/utilities/string_utils.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/utilities/string_utils.cpp.o.d"
  "PixivTagDownloader"
  "PixivTagDownloader.pdb"
)

# Per-language clean rules from dependency scanning.
foreach(lang CXX)
  include(CMakeFiles/PixivTagDownloader.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
