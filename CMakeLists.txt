cmake_minimum_required(VERSION 3.15)

# 项目基本信息
project(PixivTagDownloader
    VERSION 1.0.0
    DESCRIPTION "A C++ application for downloading artworks from Pixiv based on user-specified criteria"
    LANGUAGES CXX
)

# C++20 标准要求
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 编译选项
if(MSVC)
    add_compile_options(/W4 /utf-8)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# 构建类型默认为Release
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# 查找依赖包
find_package(PkgConfig REQUIRED)
find_package(Threads REQUIRED)

# libcurl
find_package(CURL REQUIRED)

# 尝试查找系统安装的包
find_package(nlohmann_json QUIET)
find_package(yaml-cpp QUIET)
find_package(spdlog QUIET)
find_package(CLI11 QUIET)

# 如果系统包不可用，使用FetchContent
if(NOT nlohmann_json_FOUND OR NOT yaml-cpp_FOUND OR NOT spdlog_FOUND OR NOT CLI11_FOUND)
    include(FetchContent)

    if(NOT nlohmann_json_FOUND)
        FetchContent_Declare(
            nlohmann_json
            GIT_REPOSITORY https://github.com/nlohmann/json.git
            GIT_TAG v3.11.3
        )
        FetchContent_MakeAvailable(nlohmann_json)
    endif()

    if(NOT yaml-cpp_FOUND)
        FetchContent_Declare(
            yaml-cpp
            GIT_REPOSITORY https://github.com/jbeder/yaml-cpp.git
            GIT_TAG 0.8.0
        )
        FetchContent_MakeAvailable(yaml-cpp)
    endif()

    if(NOT spdlog_FOUND)
        FetchContent_Declare(
            spdlog
            GIT_REPOSITORY https://github.com/gabime/spdlog.git
            GIT_TAG v1.12.0
        )
        FetchContent_MakeAvailable(spdlog)
    endif()

    if(NOT CLI11_FOUND)
        FetchContent_Declare(
            CLI11
            GIT_REPOSITORY https://github.com/CLIUtils/CLI11.git
            GIT_TAG v2.3.2
        )
        FetchContent_MakeAvailable(CLI11)
    endif()
endif()

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# 源文件
set(SOURCES
    src/main.cpp
    src/config/config_manager.cpp
    src/auth/pixiv_auth.cpp
    src/api/pixiv_api_client.cpp
    src/download/downloader_core.cpp
    src/storage/storage_manager.cpp
    src/cli/cli_handler.cpp
    src/core/main_controller.cpp
    src/utilities/string_utils.cpp
    src/utilities/file_utils.cpp
    src/utilities/logger.cpp
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${SOURCES})

# 链接库
target_link_libraries(${PROJECT_NAME}
    PRIVATE
    Threads::Threads
    CURL::libcurl
)

# 条件链接第三方库
if(nlohmann_json_FOUND)
    target_link_libraries(${PROJECT_NAME} PRIVATE nlohmann_json::nlohmann_json)
else()
    target_link_libraries(${PROJECT_NAME} PRIVATE nlohmann_json)
endif()

if(yaml-cpp_FOUND)
    target_link_libraries(${PROJECT_NAME} PRIVATE yaml-cpp)
else()
    target_link_libraries(${PROJECT_NAME} PRIVATE yaml-cpp)
endif()

if(spdlog_FOUND)
    target_link_libraries(${PROJECT_NAME} PRIVATE spdlog::spdlog)
else()
    target_link_libraries(${PROJECT_NAME} PRIVATE spdlog)
endif()

if(CLI11_FOUND)
    target_link_libraries(${PROJECT_NAME} PRIVATE CLI11::CLI11)
else()
    target_link_libraries(${PROJECT_NAME} PRIVATE CLI11)
endif()

# 编译器特定设置
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    target_compile_options(${PROJECT_NAME} PRIVATE -fconcepts)
endif()

# 安装规则
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)
