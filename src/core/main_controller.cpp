#include "core/main_controller.h"
#include "utilities/logger.h"
#include "utilities/file_utils.h"
#include <iostream>
#include <thread>

namespace pixiv_downloader {
namespace core {

MainController::MainController() 
    : app_state_(AppState::INITIALIZING), initialized_(false) {
}

MainController::~MainController() {
    Shutdown();
}

ExitCode MainController::Run(int argc, char* argv[]) {
    try {
        SetState(AppState::INITIALIZING);
        
        // 创建配置管理器
        config_manager_ = std::make_unique<config::ConfigManager>();
        
        // 解析命令行参数
        cli_handler_ = std::make_unique<cli::CliHandler>(*config_manager_);
        auto args = cli_handler_->ParseCommandLine(argc, argv);
        
        // 处理帮助和版本信息
        if (args.help) {
            cli_handler_->ShowHelp();
            return ExitCode::SUCCESS;
        }
        
        if (args.version) {
            cli_handler_->ShowVersion();
            return ExitCode::SUCCESS;
        }
        
        // 初始化应用程序
        if (!Initialize(args.config_path)) {
            return ExitCode::CONFIG_ERROR;
        }
        
        SetState(AppState::READY);
        
        // 根据参数决定运行模式
        if (args.interactive || args.uid.empty()) {
            return RunInteractiveMode();
        } else {
            return RunCommandLineMode(args);
        }
        
    } catch (const std::exception& e) {
        return HandleException(e);
    }
}

bool MainController::Initialize(const std::string& config_path) {
    try {
        // 加载配置
        if (!config_manager_->LoadConfig(config_path)) {
            SetError("配置文件加载失败");
            return false;
        }
        
        // 初始化日志系统
        if (!InitializeLogging()) {
            SetError("日志系统初始化失败");
            return false;
        }
        
        LOG_INFO("PixivTagDownloader 启动中...");
        
        // 创建认证管理器
        pixiv_auth_ = std::make_unique<auth::PixivAuth>(config_manager_->GetConfig().auth);
        
        // 验证认证
        if (!ValidateAuthentication()) {
            SetError("Pixiv认证失败");
            return false;
        }
        
        // 创建API客户端
        api_client_ = std::make_unique<api::PixivApiClient>(
            config_manager_->GetConfig(), *pixiv_auth_);

        if (!api_client_->Initialize()) {
            SetError("API客户端初始化失败");
            return false;
        }

        // 重新创建CLI处理器以传入API客户端
        cli_handler_ = std::make_unique<cli::CliHandler>(*config_manager_, api_client_.get());
        
        // 创建存储管理器
        storage_manager_ = std::make_unique<storage::StorageManager>(
            config_manager_->GetConfig());
        
        if (!storage_manager_->Initialize()) {
            SetError("存储管理器初始化失败");
            return false;
        }
        
        // 创建下载器
        downloader_ = std::make_unique<download::DownloaderCore>(
            config_manager_->GetConfig(), *api_client_, *storage_manager_);
        
        if (!downloader_->Initialize()) {
            SetError("下载器初始化失败");
            return false;
        }
        
        initialized_ = true;
        LOG_INFO("应用程序初始化完成");
        return true;
        
    } catch (const std::exception& e) {
        SetError("初始化过程中发生异常: " + std::string(e.what()));
        return false;
    }
}

void MainController::Shutdown() {
    if (initialized_) {
        LOG_INFO("正在关闭应用程序...");
        
        SetState(AppState::STOPPING);
        
        // 停止下载器
        if (downloader_) {
            downloader_->Stop(true);
        }
        
        // 清理API客户端
        if (api_client_) {
            api_client_->Cleanup();
        }
        
        // 清理资源
        CleanupResources();
        
        SetState(AppState::STOPPED);
        LOG_INFO("应用程序已关闭");
        
        // 关闭日志系统
        utilities::Logger::Shutdown();
        
        initialized_ = false;
    }
}

ExitCode MainController::RunInteractiveMode() {
    try {
        cli_handler_->ShowWelcome();
        
        bool continue_running = true;
        while (continue_running) {
            // 获取用户选择
            auto selection = cli_handler_->RunInteractiveMode();
            
            // 确认选择
            if (!cli_handler_->ConfirmSelection(selection)) {
                continue;
            }
            
            // 执行下载任务
            auto result = ExecuteDownloadTask(selection);
            
            // 显示执行摘要
            ShowExecutionSummary(result);
            
            // 询问是否继续
            continue_running = cli_handler_->AskForNewTask();
        }
        
        return ExitCode::SUCCESS;
        
    } catch (const std::exception& e) {
        return HandleException(e);
    }
}

ExitCode MainController::RunCommandLineMode(const cli::CommandLineArgs& args) {
    try {
        // 从命令行参数创建用户选择
        auto selection = cli_handler_->CreateSelectionFromArgs(args);
        
        // 执行下载任务
        auto result = ExecuteDownloadTask(selection);
        
        // 显示执行摘要
        ShowExecutionSummary(result);
        
        return result.success ? ExitCode::SUCCESS : ExitCode::RUNTIME_ERROR;
        
    } catch (const std::exception& e) {
        return HandleException(e);
    }
}

TaskExecutionResult MainController::ExecuteDownloadTask(const cli::UserSelection& selection) {
    TaskExecutionResult result;
    result.start_time = std::chrono::steady_clock::now();
    
    try {
        SetState(AppState::RUNNING);
        
        LOG_INFO("开始执行下载任务，用户ID: {}", selection.uid);
        
        // 获取用户信息
        auto user_info = GetUserInfo(selection.uid);
        if (user_info.uid.empty()) {
            result.error_message = "无法获取用户信息";
            return result;
        }
        
        // 获取用户作品列表
        auto artworks = GetUserArtworks(selection.uid, selection);
        if (artworks.empty()) {
            result.error_message = "未找到符合条件的作品";
            return result;
        }
        
        LOG_INFO("找到 {} 个符合条件的作品", artworks.size());
        result.total_artworks = static_cast<int>(artworks.size());
        
        // 创建下载任务
        auto download_tasks = CreateDownloadTasks(artworks, user_info);
        
        // 设置进度回调
        cli_handler_->SetupProgressCallback(*downloader_);
        
        // 启动下载器
        int worker_count = config_manager_->GetConfig().concurrency;
        if (!downloader_->Start(worker_count)) {
            result.error_message = "下载器启动失败";
            return result;
        }
        
        // 添加下载任务
        downloader_->AddTasks(download_tasks);
        
        // 等待完成
        downloader_->WaitForCompletion();
        
        // 获取结果
        auto download_results = downloader_->GetAllResults();
        
        // 统计结果
        for (const auto& download_result : download_results) {
            switch (download_result.status) {
                case download::DownloadStatus::COMPLETED:
                    result.downloaded_artworks++;
                    result.total_bytes += download_result.file_size;
                    break;
                case download::DownloadStatus::FAILED:
                    result.failed_artworks++;
                    break;
                case download::DownloadStatus::SKIPPED:
                    result.skipped_artworks++;
                    break;
                default:
                    break;
            }
        }
        
        result.success = (result.failed_artworks == 0);
        
    } catch (const std::exception& e) {
        result.error_message = e.what();
        LOG_ERROR("执行下载任务时发生异常: {}", e.what());
    }
    
    result.end_time = std::chrono::steady_clock::now();
    SetState(AppState::READY);
    
    return result;
}

bool MainController::InitializeLogging() {
    const auto& log_config = config_manager_->GetConfig().logging;
    return utilities::Logger::Initialize(
        log_config.level,
        log_config.log_path,
        log_config.max_file_size,
        log_config.max_files
    );
}

bool MainController::ValidateAuthentication() {
    const auto& config = config_manager_->GetConfig();
    
    if (config.pixiv_cookie.empty()) {
        LOG_ERROR("Pixiv Cookie 未配置");
        return false;
    }
    
    if (!pixiv_auth_->SetCookieString(config.pixiv_cookie)) {
        LOG_ERROR("Cookie 设置失败");
        return false;
    }
    
    auto auth_status = pixiv_auth_->ValidateCookie();
    if (auth_status != auth::AuthStatus::AUTHENTICATED) {
        LOG_ERROR("Cookie 验证失败: {}", auth::PixivAuth::AuthStatusToString(auth_status));
        return false;
    }
    
    LOG_INFO("Pixiv 认证成功");
    return true;
}

api::UserInfo MainController::GetUserInfo(const std::string& uid) {
    return api_client_->GetUserInfo(uid);
}

std::vector<api::ArtworkInfo> MainController::GetUserArtworks(const std::string& uid, 
                                                             const cli::UserSelection& selection) {
    // 获取用户所有作品ID
    auto artwork_ids = api_client_->GetUserArtworkIds(uid);
    
    std::vector<api::ArtworkInfo> artworks;
    
    // 处理插画和漫画
    if (artwork_ids.contains("illustrations")) {
        for (const auto& pid : artwork_ids["illustrations"]) {
            auto artwork = api_client_->GetArtworkDetails(pid);
            if (!artwork.pid.empty()) {
                artworks.push_back(artwork);
            }
        }
    }
    
    // 处理小说
    if (artwork_ids.contains("novels")) {
        for (const auto& pid : artwork_ids["novels"]) {
            auto artwork = api_client_->GetNovelDetails(pid);
            if (!artwork.pid.empty()) {
                artworks.push_back(artwork);
            }
        }
    }
    
    // 应用过滤器
    return FilterArtworks(artworks, selection);
}

std::vector<api::ArtworkInfo> MainController::FilterArtworks(const std::vector<api::ArtworkInfo>& artworks,
                                                            const cli::UserSelection& selection) {
    std::vector<api::ArtworkInfo> filtered_artworks;
    
    for (const auto& artwork : artworks) {
        // 应用类型过滤
        if (!ApplyTypeFilter(artwork, selection.artwork_types)) {
            continue;
        }
        
        // 如果不是下载所有作品，应用标签过滤
        if (!selection.download_all_works) {
            if (!ApplyTagFilter(artwork, selection.tags, selection.tag_logic)) {
                continue;
            }
        }
        
        filtered_artworks.push_back(artwork);
    }
    
    return filtered_artworks;
}

bool MainController::ApplyTypeFilter(const api::ArtworkInfo& artwork,
                                   const std::vector<config::ArtworkType>& types) {
    // 如果包含ALL类型，接受所有作品
    for (const auto& type : types) {
        if (type == config::ArtworkType::ALL || type == artwork.type) {
            return true;
        }
    }
    return false;
}

bool MainController::ApplyTagFilter(const api::ArtworkInfo& artwork,
                                  const std::vector<std::string>& tags,
                                  config::TagFilterLogic logic) {
    if (tags.empty()) {
        return true; // 没有标签过滤条件
    }
    
    switch (logic) {
        case config::TagFilterLogic::AND: {
            // 所有标签都必须匹配
            for (const auto& filter_tag : tags) {
                bool found = false;
                for (const auto& artwork_tag : artwork.tags) {
                    if (artwork_tag == filter_tag) {
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    return false;
                }
            }
            return true;
        }
        
        case config::TagFilterLogic::OR: {
            // 任意标签匹配
            for (const auto& filter_tag : tags) {
                for (const auto& artwork_tag : artwork.tags) {
                    if (artwork_tag == filter_tag) {
                        return true;
                    }
                }
            }
            return false;
        }
        
        case config::TagFilterLogic::NOT: {
            // 排除包含指定标签的作品
            for (const auto& filter_tag : tags) {
                for (const auto& artwork_tag : artwork.tags) {
                    if (artwork_tag == filter_tag) {
                        return false;
                    }
                }
            }
            return true;
        }
        
        default:
            return true;
    }
}

std::vector<download::DownloadTask> MainController::CreateDownloadTasks(
    const std::vector<api::ArtworkInfo>& artworks,
    const api::UserInfo& user_info) {
    
    std::vector<download::DownloadTask> tasks;
    
    for (const auto& artwork : artworks) {
        auto artwork_tasks = CreateTasksForArtwork(artwork, user_info);
        tasks.insert(tasks.end(), artwork_tasks.begin(), artwork_tasks.end());
    }
    
    return tasks;
}

std::vector<download::DownloadTask> MainController::CreateTasksForArtwork(
    const api::ArtworkInfo& artwork,
    const api::UserInfo& user_info) {
    
    std::vector<download::DownloadTask> tasks;
    
    if (artwork.type == config::ArtworkType::NOVEL) {
        // 小说任务
        auto path_result = storage_manager_->GenerateNovelPath(artwork, user_info);
        
        download::DownloadTask task;
        task.url = ""; // 小说不需要下载URL，内容已在artwork.content中
        task.output_path = path_result.full_path;
        task.artwork_info = artwork;
        task.task_id = "novel_" + artwork.pid;
        
        tasks.push_back(task);
        
    } else {
        // 图片任务
        if (artwork.page_count == 1) {
            // 单页图片
            if (!artwork.image_urls.empty()) {
                auto path_result = storage_manager_->GenerateSingleImagePath(
                    artwork, user_info, utilities::FileUtils::GetFileExtension(artwork.image_urls[0]));
                
                download::DownloadTask task;
                task.url = artwork.image_urls[0];
                task.output_path = path_result.full_path;
                task.artwork_info = artwork;
                task.page_index = 0;
                task.task_id = "image_" + artwork.pid + "_p0";
                
                tasks.push_back(task);
            }
        } else {
            // 多页图片
            for (int i = 0; i < artwork.page_count && i < static_cast<int>(artwork.image_urls.size()); ++i) {
                auto path_result = storage_manager_->GenerateMultiImagePath(
                    artwork, user_info, i, utilities::FileUtils::GetFileExtension(artwork.image_urls[i]));
                
                download::DownloadTask task;
                task.url = artwork.image_urls[i];
                task.output_path = path_result.full_path;
                task.artwork_info = artwork;
                task.page_index = i;
                task.task_id = "image_" + artwork.pid + "_p" + std::to_string(i);
                
                tasks.push_back(task);
            }
        }
    }
    
    return tasks;
}

void MainController::ShowExecutionSummary(const TaskExecutionResult& result) {
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        result.end_time - result.start_time).count();
    
    std::cout << "\n=== 下载完成摘要 ===" << std::endl;
    std::cout << "总作品数: " << result.total_artworks << std::endl;
    std::cout << "成功下载: " << result.downloaded_artworks << std::endl;
    std::cout << "下载失败: " << result.failed_artworks << std::endl;
    std::cout << "跳过文件: " << result.skipped_artworks << std::endl;
    std::cout << "总用时: " << duration << " 秒" << std::endl;
    
    if (!result.error_message.empty()) {
        std::cout << "错误信息: " << result.error_message << std::endl;
    }
    
    std::cout << "===================" << std::endl;
}

ExitCode MainController::HandleException(const std::exception& e) {
    SetError(e.what());
    LOG_FATAL("程序异常: {}", e.what());
    cli_handler_->ShowError("程序发生异常: " + std::string(e.what()));
    return ExitCode::RUNTIME_ERROR;
}

void MainController::CleanupResources() {
    // 清理各种资源
    downloader_.reset();
    storage_manager_.reset();
    api_client_.reset();
    pixiv_auth_.reset();
    cli_handler_.reset();
    config_manager_.reset();
}

void MainController::SetState(AppState state) {
    app_state_ = state;
    LOG_DEBUG("应用程序状态更新为: {}", AppStateToString(state));
}

void MainController::SetError(const std::string& error) {
    last_error_ = error;
    LOG_ERROR(error);
}

std::string MainController::ExitCodeToString(ExitCode code) {
    switch (code) {
        case ExitCode::SUCCESS: return "成功";
        case ExitCode::CONFIG_ERROR: return "配置错误";
        case ExitCode::AUTH_ERROR: return "认证错误";
        case ExitCode::NETWORK_ERROR: return "网络错误";
        case ExitCode::STORAGE_ERROR: return "存储错误";
        case ExitCode::USER_CANCELLED: return "用户取消";
        case ExitCode::INVALID_ARGUMENTS: return "无效参数";
        case ExitCode::RUNTIME_ERROR: return "运行时错误";
        default: return "未知错误";
    }
}

std::string MainController::AppStateToString(AppState state) {
    switch (state) {
        case AppState::INITIALIZING: return "初始化中";
        case AppState::READY: return "就绪";
        case AppState::RUNNING: return "运行中";
        case AppState::PAUSED: return "暂停";
        case AppState::STOPPING: return "停止中";
        case AppState::STOPPED: return "已停止";
        case AppState::ERROR: return "错误";
        default: return "未知状态";
    }
}

} // namespace core
} // namespace pixiv_downloader
