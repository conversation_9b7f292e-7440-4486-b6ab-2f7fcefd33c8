/usr/bin/c++ -O3 -DNDEBUG -Wl,--dependency-file=CMakeFiles/PixivTagDownloader.dir/link.d CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.o CMakeFiles/PixivTagDownloader.dir/src/auth/pixiv_auth.cpp.o CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api_client.cpp.o CMakeFiles/PixivTagDownloader.dir/src/download/downloader_core.cpp.o CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o CMakeFiles/PixivTagDownloader.dir/src/cli/cli_handler.cpp.o CMakeFiles/PixivTagDownloader.dir/src/core/main_controller.cpp.o CMakeFiles/PixivTagDownloader.dir/src/utilities/string_utils.cpp.o CMakeFiles/PixivTagDownloader.dir/src/utilities/file_utils.cpp.o CMakeFiles/PixivTagDownloader.dir/src/utilities/logger.cpp.o -o PixivTagDownloader  /usr/lib/libcurl.so -lyaml-cpp /usr/lib/libspdlog.so.1.15.3 /usr/lib/libcurl.so /usr/lib/libfmt.so.11.2.0
