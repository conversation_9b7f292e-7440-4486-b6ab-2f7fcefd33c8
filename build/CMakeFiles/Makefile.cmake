# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/mnt/wd500g/PixivTagDownloader-CPP/CMakeLists.txt"
  "CMakeFiles/4.0.3-dirty/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.0.3-dirty/CMakeSystem.cmake"
  "/usr/lib/cmake/fmt/fmt-config-version.cmake"
  "/usr/lib/cmake/fmt/fmt-config.cmake"
  "/usr/lib/cmake/fmt/fmt-targets-none.cmake"
  "/usr/lib/cmake/fmt/fmt-targets.cmake"
  "/usr/lib/cmake/spdlog/spdlogConfig.cmake"
  "/usr/lib/cmake/spdlog/spdlogConfigTargets-none.cmake"
  "/usr/lib/cmake/spdlog/spdlogConfigTargets.cmake"
  "/usr/lib/cmake/spdlog/spdlogConfigVersion.cmake"
  "/usr/lib/cmake/yaml-cpp/yaml-cpp-config-version.cmake"
  "/usr/lib/cmake/yaml-cpp/yaml-cpp-config.cmake"
  "/usr/lib/cmake/yaml-cpp/yaml-cpp-targets-release.cmake"
  "/usr/lib/cmake/yaml-cpp/yaml-cpp-targets.cmake"
  "/usr/share/cmake/CLI11/CLI11Config.cmake"
  "/usr/share/cmake/CLI11/CLI11ConfigVersion.cmake"
  "/usr/share/cmake/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake"
  "/usr/share/cmake/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake/Modules/CheckCXXSourceCompiles.cmake"
  "/usr/share/cmake/Modules/CheckIncludeFileCXX.cmake"
  "/usr/share/cmake/Modules/CheckLibraryExists.cmake"
  "/usr/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake/Modules/FindCURL.cmake"
  "/usr/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/share/cmake/Modules/FindPackageMessage.cmake"
  "/usr/share/cmake/Modules/FindPkgConfig.cmake"
  "/usr/share/cmake/Modules/FindThreads.cmake"
  "/usr/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake"
  "/usr/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "/usr/share/cmake/Modules/Internal/CheckSourceCompiles.cmake"
  "/usr/share/cmake/Modules/Linker/GNU-CXX.cmake"
  "/usr/share/cmake/Modules/Linker/GNU.cmake"
  "/usr/share/cmake/Modules/Platform/Linker/GNU.cmake"
  "/usr/share/cmake/Modules/Platform/Linker/Linux-GNU-CXX.cmake"
  "/usr/share/cmake/Modules/Platform/Linker/Linux-GNU.cmake"
  "/usr/share/cmake/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/share/cmake/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake/Modules/Platform/Linux-Initialize.cmake"
  "/usr/share/cmake/Modules/Platform/Linux.cmake"
  "/usr/share/cmake/Modules/Platform/UnixPaths.cmake"
  "/usr/share/cmake/Modules/SelectLibraryConfigurations.cmake"
  "/usr/share/cmake/nlohmann_json/nlohmann_jsonConfig.cmake"
  "/usr/share/cmake/nlohmann_json/nlohmann_jsonConfigVersion.cmake"
  "/usr/share/cmake/nlohmann_json/nlohmann_jsonTargets.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/PixivTagDownloader.dir/DependInfo.cmake"
  )
