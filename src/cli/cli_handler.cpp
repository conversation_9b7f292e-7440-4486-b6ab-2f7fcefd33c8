#include "cli/cli_handler.h"
#include "utilities/logger.h"
#include "utilities/string_utils.h"
#include <iostream>
#include <iomanip>
#include <csignal>

namespace pixiv_downloader {
namespace cli {

// 静态成员初始化
CliHandler* CliHandler::instance_ = nullptr;
download::DownloaderCore* CliHandler::downloader_instance_ = nullptr;

CliHandler::CliHandler(config::ConfigManager& config_manager)
    : config_manager_(config_manager), signal_received_(false), signal_count_(0) {
    
    instance_ = this;
    
    // 创建CLI应用
    app_ = std::make_unique<CLI::App>("PixivTagDownloader - Pixiv作品下载器");
    
    // 创建进度显示器
    progress_display_ = std::make_unique<ProgressDisplay>();
}

CliHandler::~CliHandler() {
    instance_ = nullptr;
    downloader_instance_ = nullptr;
}

CommandLineArgs CliHandler::ParseCommandLine(int argc, char* argv[]) {
    CommandLineArgs args;
    
    // 配置命令行选项
    app_->add_option("-u,--uid", args.uid, "目标Pixiv用户ID（必需）");
    app_->add_option("-t,--tags", args.tags, "标签列表（逗号分隔）");
    app_->add_option("-l,--logic", args.tag_logic, "标签过滤逻辑 (and|or|not)")
        ->default_val("or");
    app_->add_flag("--all-works", args.all_works, "下载所有作品");
    app_->add_option("-T,--type", args.artwork_types, "作品类型 (illust|manga|novel|all)")
        ->default_val("all");
    app_->add_option("-c,--config", args.config_path, "配置文件路径")
        ->default_val("config.yaml");
    app_->add_option("--output-dir", args.output_dir, "输出根目录");
    app_->add_option("--download-method", args.download_method, "下载方式 (direct|aria2c)");
    app_->add_option("--threads,--concurrency", args.concurrency, "并发下载数");
    app_->add_option("--delay", args.delay_range, "随机延迟范围 (如: 1-3)");
    app_->add_option("--file-conflict", args.file_conflict, "文件冲突策略 (skip|overwrite|rename)");
    app_->add_option("--log-level", args.log_level, "日志级别 (trace|debug|info|warn|error)");
    app_->add_option("--log-path", args.log_path, "日志文件路径");
    app_->add_flag("-i,--interactive", args.interactive, "强制交互模式");
    app_->add_flag("--version", args.version, "显示版本信息");
    
    try {
        app_->parse(argc, argv);
    } catch (const CLI::ParseError& e) {
        std::cerr << "命令行参数解析错误: " << e.what() << std::endl;
        args.help = true;
    }
    
    return args;
}

UserSelection CliHandler::RunInteractiveMode() {
    UserSelection selection;
    
    std::cout << "\n=== PixivTagDownloader 交互模式 ===" << std::endl;
    
    // 1. 选择作品类型
    selection.artwork_types = SelectArtworkTypes();
    
    // 2. 输入用户ID
    selection.uid = InputUserId();
    
    // 3. 选择下载方式
    int download_method = SelectDownloadMethod();
    
    if (download_method == 1) {
        // 下载所有作品
        selection.download_all_works = true;
    } else if (download_method == 2) {
        // 手动输入标签
        selection.tags = InputTagsManually();
        selection.tag_logic = SelectTagFilterLogic();
    } else if (download_method == 3) {
        // 从用户作品中选择标签
        selection.tags = SelectTagsFromUser(selection.uid);
        selection.tag_logic = SelectTagFilterLogic();
    }
    
    return selection;
}

UserSelection CliHandler::CreateSelectionFromArgs(const CommandLineArgs& args) {
    UserSelection selection;
    
    selection.uid = args.uid;
    selection.download_all_works = args.all_works;
    
    // 解析标签
    if (!args.tags.empty()) {
        for (const auto& tag_str : args.tags) {
            auto tags = utilities::StringUtils::Split(tag_str, ",");
            for (auto& tag : tags) {
                tag = utilities::StringUtils::Trim(tag);
                if (!tag.empty()) {
                    selection.tags.push_back(tag);
                }
            }
        }
    }
    
    // 解析标签逻辑
    selection.tag_logic = config::ConfigManager::StringToTagFilterLogic(args.tag_logic);
    
    // 解析作品类型
    selection.artwork_types = config::ConfigManager::StringToArtworkTypes(args.artwork_types);
    
    return selection;
}

void CliHandler::ShowHelp() {
    std::cout << app_->help() << std::endl;
    
    std::cout << "\n使用示例:" << std::endl;
    std::cout << "  # 交互模式" << std::endl;
    std::cout << "  PixivTagDownloader" << std::endl;
    std::cout << "  " << std::endl;
    std::cout << "  # 下载指定用户的所有作品" << std::endl;
    std::cout << "  PixivTagDownloader -u 12345678 --all-works" << std::endl;
    std::cout << "  " << std::endl;
    std::cout << "  # 下载指定用户的特定标签作品" << std::endl;
    std::cout << "  PixivTagDownloader -u 12345678 -t \"原创,插画\" -l or" << std::endl;
    std::cout << "  " << std::endl;
    std::cout << "  # 仅下载插画类型" << std::endl;
    std::cout << "  PixivTagDownloader -u 12345678 -T illust --all-works" << std::endl;
}

void CliHandler::ShowVersion() {
    std::cout << "PixivTagDownloader v1.0.0" << std::endl;
    std::cout << "一个高效的Pixiv作品下载器" << std::endl;
    std::cout << "构建时间: " << __DATE__ << " " << __TIME__ << std::endl;
}

void CliHandler::ShowWelcome() {
    std::cout << R"(
 ____  _       _       _____             ____                      _                 _           
|  _ \(_)_  __(_)_   _|_   _|_ _  __ _   |  _ \  _____      ___ __ | | ___   __ _  __| | ___ _ __ 
| |_) | \ \/ /| \ \ / / | |/ _` |/ _` |  | | | |/ _ \ \ /\ / / '_ \| |/ _ \ / _` |/ _` |/ _ \ '__|
|  __/| |>  < | |\ V /  | | (_| | (_| |  | |_| | (_) \ V  V /| | | | | (_) | (_| | (_| |  __/ |   
|_|   |_/_/\_\|_| \_/   |_|\__,_|\__, |  |____/ \___/ \_/\_/ |_| |_|_|\___/ \__,_|\__,_|\___|_|   
                                 |___/                                                            
)" << std::endl;
    
    std::cout << "欢迎使用 PixivTagDownloader!" << std::endl;
    std::cout << "这是一个用于从Pixiv下载作品的工具。" << std::endl;
    std::cout << std::endl;
}

bool CliHandler::ConfirmSelection(const UserSelection& selection) {
    std::cout << "\n=== 确认您的选择 ===" << std::endl;
    std::cout << "用户ID: " << selection.uid << std::endl;
    
    std::cout << "作品类型: ";
    std::vector<std::string> type_names;
    for (const auto& type : selection.artwork_types) {
        type_names.push_back(config::ConfigManager::ArtworkTypeToString(type));
    }
    std::cout << utilities::StringUtils::Join(type_names, ", ") << std::endl;
    
    if (selection.download_all_works) {
        std::cout << "下载方式: 所有作品" << std::endl;
    } else {
        std::cout << "下载方式: 按标签筛选" << std::endl;
        std::cout << "标签: " << utilities::StringUtils::Join(selection.tags, ", ") << std::endl;
        std::cout << "过滤逻辑: " << config::ConfigManager::TagFilterLogicToString(selection.tag_logic) << std::endl;
    }
    
    return GetUserConfirmation("是否继续下载?", true);
}

bool CliHandler::AskForNewTask() {
    std::cout << std::endl;
    return GetUserConfirmation("是否要下载其他用户的作品?", false);
}

void CliHandler::SetupProgressCallback(download::DownloaderCore& downloader) {
    downloader_instance_ = &downloader;
    
    downloader.SetProgressCallback([this](const download::DownloadProgress& progress) {
        progress_display_->UpdateProgress(progress);
    });
    
    downloader.SetTaskCompletionCallback([this](const download::DownloadResult& result) {
        progress_display_->ShowTaskCompletion(result);
    });
    
    progress_display_->Start();
}

void CliHandler::SetupSignalHandlers(download::DownloaderCore& downloader) {
    downloader_instance_ = &downloader;
    
    // 设置信号处理器
    std::signal(SIGINT, SignalHandler);
    std::signal(SIGTERM, SignalHandler);
}

void CliHandler::ShowError(const std::string& message) {
    std::cerr << "错误: " << message << std::endl;
}

void CliHandler::ShowWarning(const std::string& message) {
    std::cout << "警告: " << message << std::endl;
}

void CliHandler::ShowInfo(const std::string& message) {
    std::cout << "信息: " << message << std::endl;
}

// 私有方法实现
std::string CliHandler::GetUserInput(const std::string& prompt, const std::string& default_value) {
    std::cout << prompt;
    if (!default_value.empty()) {
        std::cout << " [" << default_value << "]";
    }
    std::cout << ": ";
    
    std::string input;
    std::getline(std::cin, input);
    
    input = utilities::StringUtils::Trim(input);
    return input.empty() ? default_value : input;
}

bool CliHandler::GetUserConfirmation(const std::string& prompt, bool default_yes) {
    std::string suffix = default_yes ? " (Y/n)" : " (y/N)";
    std::string input = GetUserInput(prompt + suffix);
    
    if (input.empty()) {
        return default_yes;
    }
    
    std::string lower_input = utilities::StringUtils::ToLower(input);
    return (lower_input == "y" || lower_input == "yes");
}

std::vector<config::ArtworkType> CliHandler::SelectArtworkTypes() {
    std::cout << "\n选择要下载的作品类型 (可多选，用逗号分隔):" << std::endl;
    std::cout << "1. 插画 (Illust)" << std::endl;
    std::cout << "2. 漫画 (Manga)" << std::endl;
    std::cout << "3. 小说 (Novel)" << std::endl;
    std::cout << "4. 全部 (All)" << std::endl;
    
    std::string input = GetUserInput("请输入选择", "4");
    
    std::vector<config::ArtworkType> types;
    auto choices = utilities::StringUtils::Split(input, ",");
    
    for (auto& choice : choices) {
        choice = utilities::StringUtils::Trim(choice);
        if (choice == "1") types.push_back(config::ArtworkType::ILLUST);
        else if (choice == "2") types.push_back(config::ArtworkType::MANGA);
        else if (choice == "3") types.push_back(config::ArtworkType::NOVEL);
        else if (choice == "4") return {config::ArtworkType::ALL};
    }
    
    return types.empty() ? std::vector<config::ArtworkType>{config::ArtworkType::ALL} : types;
}

std::string CliHandler::InputUserId() {
    std::string uid;
    do {
        uid = GetUserInput("\n请输入画师的Pixiv用户ID (UID)");
        if (!ValidateUserId(uid)) {
            std::cout << "无效的用户ID，请输入数字。" << std::endl;
            uid.clear();
        }
    } while (uid.empty());
    
    return uid;
}

int CliHandler::SelectDownloadMethod() {
    std::cout << "\n选择下载方式:" << std::endl;
    std::cout << "1. 下载所有作品" << std::endl;
    std::cout << "2. 按手动输入的标签筛选" << std::endl;
    std::cout << "3. 从用户现有作品的标签列表中选择" << std::endl;
    
    std::string input = GetUserInput("请选择", "1");
    
    int choice = 1;
    try {
        choice = std::stoi(input);
        if (choice < 1 || choice > 3) {
            choice = 1;
        }
    } catch (...) {
        choice = 1;
    }
    
    return choice;
}

std::vector<std::string> CliHandler::InputTagsManually() {
    std::string input = GetUserInput("\n请输入要下载的标签 (用逗号分隔)");
    
    auto tags = utilities::StringUtils::Split(input, ",");
    for (auto& tag : tags) {
        tag = utilities::StringUtils::Trim(tag);
    }
    
    // 移除空标签
    tags.erase(std::remove_if(tags.begin(), tags.end(), 
        [](const std::string& tag) { return tag.empty(); }), tags.end());
    
    return tags;
}

std::vector<std::string> CliHandler::SelectTagsFromUser(const std::string& uid) {
    std::cout << "\n正在获取用户标签... (这可能需要一些时间)" << std::endl;
    
    // 这里应该调用API获取用户标签
    // 暂时返回空列表，实际实现需要API客户端
    std::vector<std::string> tags;
    
    std::cout << "暂未实现从用户作品获取标签功能，请使用手动输入。" << std::endl;
    return InputTagsManually();
}

config::TagFilterLogic CliHandler::SelectTagFilterLogic() {
    std::cout << "\n选择标签筛选逻辑:" << std::endl;
    std::cout << "1. AND (作品需包含所有选定标签)" << std::endl;
    std::cout << "2. OR (作品只需包含任意一个选定标签)" << std::endl;
    std::cout << "3. NOT (排除包含指定标签的作品)" << std::endl;
    
    std::string input = GetUserInput("请选择", "2");
    
    int choice = 2;
    try {
        choice = std::stoi(input);
    } catch (...) {
        choice = 2;
    }
    
    switch (choice) {
        case 1: return config::TagFilterLogic::AND;
        case 3: return config::TagFilterLogic::NOT;
        default: return config::TagFilterLogic::OR;
    }
}

bool CliHandler::ValidateUserId(const std::string& uid) {
    return !uid.empty() && utilities::StringUtils::IsNumeric(uid);
}

void CliHandler::SignalHandler(int signal) {
    if (instance_) {
        instance_->signal_received_ = true;
        instance_->signal_count_++;
        
        if (instance_->signal_count_ == 1) {
            std::cout << "\n检测到 Ctrl-C。正在尝试优雅关闭..." << std::endl;
            if (downloader_instance_) {
                downloader_instance_->Stop(true);
            }
        } else if (instance_->signal_count_ >= 2) {
            std::cout << "\n强制退出程序。" << std::endl;
            std::exit(1);
        }
    }
}

// ProgressDisplay 实现
ProgressDisplay::ProgressDisplay() : is_running_(false), verbose_(false) {
}

ProgressDisplay::~ProgressDisplay() {
    Stop();
}

void ProgressDisplay::Start() {
    is_running_ = true;
}

void ProgressDisplay::Stop() {
    is_running_ = false;
}

void ProgressDisplay::UpdateProgress(const download::DownloadProgress& progress) {
    if (!is_running_) return;
    
    std::lock_guard<std::mutex> lock(display_mutex_);
    
    // 清除当前行
    ClearCurrentLine();
    
    // 显示总体进度
    std::cout << "进度: " << progress.completed_tasks << "/" << progress.total_tasks 
              << " (" << std::fixed << std::setprecision(1) << progress.overall_progress << "%)";
    
    if (!progress.current_file.empty()) {
        std::cout << " - " << progress.current_file;
        if (progress.current_file_progress > 0) {
            std::cout << " (" << std::fixed << std::setprecision(1) 
                      << progress.current_file_progress << "%)";
        }
    }
    
    std::cout << std::flush;
}

void ProgressDisplay::ShowTaskCompletion(const download::DownloadResult& result) {
    if (!verbose_) return;
    
    std::lock_guard<std::mutex> lock(display_mutex_);
    
    ClearCurrentLine();
    
    switch (result.status) {
        case download::DownloadStatus::COMPLETED:
            std::cout << "✓ 完成: " << result.file_path << std::endl;
            break;
        case download::DownloadStatus::FAILED:
            std::cout << "✗ 失败: " << result.file_path 
                      << " (" << result.error_message << ")" << std::endl;
            break;
        case download::DownloadStatus::SKIPPED:
            std::cout << "- 跳过: " << result.file_path << std::endl;
            break;
        default:
            break;
    }
}

void ProgressDisplay::ShowFinalStatistics(const std::vector<download::DownloadResult>& results) {
    std::lock_guard<std::mutex> lock(display_mutex_);
    
    ClearCurrentLine();
    
    int completed = 0, failed = 0, skipped = 0;
    size_t total_bytes = 0;
    
    for (const auto& result : results) {
        switch (result.status) {
            case download::DownloadStatus::COMPLETED:
                completed++;
                total_bytes += result.file_size;
                break;
            case download::DownloadStatus::FAILED:
                failed++;
                break;
            case download::DownloadStatus::SKIPPED:
                skipped++;
                break;
            default:
                break;
        }
    }
    
    std::cout << "\n下载统计:" << std::endl;
    std::cout << "成功: " << completed << std::endl;
    std::cout << "失败: " << failed << std::endl;
    std::cout << "跳过: " << skipped << std::endl;
    std::cout << "总大小: " << FormatFileSize(total_bytes) << std::endl;
}

void ProgressDisplay::ClearCurrentLine() {
    std::cout << "\r\033[K";
}

std::string ProgressDisplay::FormatFileSize(size_t bytes) {
    const char* units[] = {"B", "KB", "MB", "GB", "TB"};
    int unit = 0;
    double size = static_cast<double>(bytes);
    
    while (size >= 1024.0 && unit < 4) {
        size /= 1024.0;
        unit++;
    }
    
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(2) << size << " " << units[unit];
    return oss.str();
}

} // namespace cli
} // namespace pixiv_downloader
