# PixivTagDownloader 配置文件
# 请根据需要修改以下配置项

# Pixiv认证Cookie（必需）
# 从浏览器中复制完整的Cookie字符串
pixiv_cookie: ""

# 下载配置
download_method: "direct"  # 下载方式: direct, aria2c
aria2c_path: "aria2c"      # aria2c可执行文件路径
aria2c_options: "--continue=true --max-connection-per-server=4"
concurrency: 4             # 并发下载数

# HTTP配置
http:
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
  referer: "https://www.pixiv.net/"
  connection_timeout: 30   # 连接超时（秒）
  read_timeout: 60         # 读取超时（秒）
  max_retries: 3           # 最大重试次数
  retry_delay: 2           # 重试延迟（秒）
  custom_headers: {}       # 自定义HTTP头部

# 请求延迟配置
delay:
  min_delay: 1             # 最小延迟（秒）
  max_delay: 3             # 最大延迟（秒）

# 文件冲突处理策略
file_conflict: "skip"      # skip, overwrite, rename

# 路径和文件命名模板
paths:
  output_root_dir: "./downloads"
  artwork_path_template: "{output_root_dir}/{uid}_{username}/{type}/"
  single_image_naming_template: "{upload_date}_{pid}_p0_{title}{ext}"
  multi_image_subfolder_naming_template: "{pid}_{title}/"
  multi_image_file_naming_template: "{page_index}_{title}{ext}"
  novel_path_template: "{output_root_dir}/{uid}_{username}/Novel/{series_title|No_Series}/"
  novel_naming_template: "{upload_date}_{pid}_{title}.txt"
  metadata_filename_template: "{pid}_info.txt"
  page_index_format: "p%02d"  # 页码格式
  date_format: "%Y%m%d"       # 日期格式
  tag_separator: "_"          # 标签分隔符

# 日志配置
logging:
  level: "info"            # trace, debug, info, warn, error
  log_path: ""             # 日志文件路径（空表示仅控制台输出）
  max_file_size: 10485760  # 最大文件大小（字节）
  max_files: 3             # 最大文件数量

# 默认命令行参数
defaults:
  uid: ""
  tags: []
  tag_logic: "or"          # and, or, not
  types: "all"             # illust, manga, novel, all
  all_works: false
